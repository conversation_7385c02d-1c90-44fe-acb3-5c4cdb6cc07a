using System.Diagnostics;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Lottery;

/// <summary>
/// 期号时间服务实现类 - 企业级期号管理服务
/// 提供完整的期号时间管理功能：期号生成、时间计算、缓存管理
/// 包含完整的业务规则、异常处理、性能监控、数据缓存
/// 支持异步操作、并发安全、实时更新机制
/// 注：投注状态管理已迁移至RuntimeConfiguration.CanBet统一控制
/// </summary>
public class IssueTimeService(ILogger<DrawService> logger, IFreeSql fSql) : IIssueTimeService
{
    #region 业务常量定义

    /// <summary>
    /// 每天期号发放次数
    ///
    /// 业务规则：每天固定发放203期
    /// 计算依据：从早上7点到次日6点55分，每5分钟一期
    /// 时间跨度：24小时 × 60分钟 ÷ 5分钟间隔 = 288期（理论值）
    /// 实际设置：203期（根据业务需求调整）
    ///
    /// 影响范围：
    /// - 数据库表记录数量
    /// - 内存缓存大小
    /// - 期号生成逻辑
    /// </summary>
    private const int DailyIssueCount = 203;

    /// <summary>
    /// 期号间隔时间（分钟）
    ///
    /// 业务规则：每期间隔5分钟
    /// 设计考虑：
    /// - 给用户足够的投注时间
    /// - 保持合理的开奖频率
    /// - 便于时间计算和显示
    ///
    /// 相关计算：
    /// - 下一期开放时间 = 当前期关闭时间 + 间隔时间
    /// - 每小时期数 = 60分钟 ÷ 5分钟 = 12期
    /// </summary>
    private const int IntervalMinutes = 5;

    /// <summary>
    /// 每期投注持续时间（分钟）
    ///
    /// 业务规则：每期投注时间为5分钟
    /// 时间分配：
    /// - 投注时间：5分钟
    /// - 间隔时间：0分钟（无间隔，连续开放）
    ///
    /// 实际投注时间：
    /// - 开放时间：期号开始时间
    /// - 关闭时间：开始时间 + 5分钟 - 10秒（提前关闭）
    /// - 有效投注时间：4分50秒
    /// </summary>
    private const int DurationMinutes = 5;

    /// <summary>
    /// 提前关闭投注时间（秒）
    ///
    /// 业务规则：在期号结束前10秒停止接受投注
    /// 设计目的：
    /// - 为开奖准备留出缓冲时间
    /// - 避免最后时刻的投注冲突
    /// - 确保开奖流程的稳定性
    ///
    /// 实际效果：
    /// - 名义投注时间：5分钟
    /// - 实际投注时间：4分50秒
    /// - 开奖准备时间：10秒
    /// </summary>
    private const int EarlyCloseSeconds = 10;

    /// <summary>
    /// 每天开始发放的小时
    ///
    /// 业务规则：每天早上7点开始第一期
    /// 选择依据：
    /// - 符合用户作息习惯
    /// - 避开深夜时段
    /// - 便于系统维护和管理
    ///
    /// 时间计算：
    /// - 第一期开放时间：每天07:00:00
    /// - 最后一期时间：根据期数和间隔自动计算
    /// - 跨日处理：自动处理跨越午夜的情况
    /// </summary>
    private const int StartHour = 7;

    /// <summary>
    /// 民国元年（公元1912年）
    ///
    /// 历史背景：中华民国成立于1912年，民国纪年从此开始
    /// 计算公式：民国年份 = 公元年份 - 1911
    ///
    /// 期号格式：民国年份 + 6位序号
    /// 示例：
    /// - 2024年 → 民国113年 → 期号前缀：113
    /// - 第1期 → 113000001
    /// - 第203期 → 113000203
    ///
    /// 设计优势：
    /// - 期号长度固定，便于排序和查询
    /// - 包含年份信息，便于数据管理
    /// - 符合传统习惯，用户易于理解
    /// </summary>
    private const int RepublicOfChinaFirstYear = 1912;

    #endregion

    #region 私有字段和依赖

    /// <summary>
    /// 缓存当前期号信息
    /// </summary>
    private IssueTime? _cachedCurrentIssueTime = new();

    /// <summary>
    /// 缓存当前开放时间
    /// </summary>
    private int _openTimeSpan;

    /// <summary>
    /// 缓存当前关闭时间
    /// </summary>
    private int _closeTimeSpan;

    /// <summary>
    /// 读写锁对象
    /// </summary>
    private readonly ReaderWriterLockSlim _cacheRwLock = new();

    #endregion

    #region 公共缓存访问方法

    /// <summary>
    /// 线程安全地获取当前缓存的发放时间信息
    ///
    /// 功能：为多线程环境提供安全的缓存读取接口
    ///
    /// 性能优化：
    /// - 使用读写锁，允许多个线程并发读取
    /// - 读取操作不会阻塞其他读取操作
    /// - 只有在写入时才会阻塞读取
    ///
    /// 线程安全：
    /// - 使用ReaderWriterLockSlim确保读取安全
    /// - 防止在读取过程中缓存被修改
    /// - 返回的是引用，调用方不应修改返回的对象
    ///
    /// 使用场景：
    /// - 多个业务模块需要获取当前期号信息
    /// - 高频率的期号状态查询
    /// - 实时显示当前期号和时间信息
    ///
    /// 注意事项：
    /// - 返回null表示缓存未初始化或已清除
    /// - 返回的对象不应被修改，以保持缓存一致性
    /// - 调用方应该检查返回值是否为null
    /// </summary>
    /// <returns>当前缓存的发放时间对象，如果缓存为空则返回null</returns>
    public IssueTime? GetCurrentCachedIssueTime()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            return _cachedCurrentIssueTime;
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }

    /// <summary>
    /// 线程安全地获取当前缓存的开盘倒计时
    /// </summary>
    public int GetCurrentCachedOpenTimeSpan()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            return _openTimeSpan;
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }

    /// <summary>
    /// 线程安全地获取当前缓存的封盘倒计时
    /// </summary>
    public int GetCurrentCachedCloseTimeSpan()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            return _closeTimeSpan;
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }



    #endregion

    #region 数据创建和管理方法

    /// <summary>
    /// 创建期号时间记录 - 初始化年度期号数据
    ///
    /// 功能：
    /// - 生成一整年的期号时间数据
    /// - 批量插入到IssueTime表
    /// - 每天203期，每期间隔5分钟
    /// - 从早上7点开始到次日6点55分
    ///
    /// 业务规则：
    /// - 期号格式：民国年份 + 3位序号（如：113000001）
    /// - 时间间隔：每5分钟一期
    /// - 投注时长：每期4分50秒（提前10秒封盘）
    ///
    /// 返回值：返回第一期的期号时间信息
    /// </summary>
    public async Task<IssueTime> CreateIssueTimeAsync()
    {
        try
        {
            // 生成全年发放时间数据
            logger.LogDebug("开始创建发放时间数据");
            var issueTimeList = GenerateYearlyIssueTimeData();
            logger.LogInformation($"生成了 {issueTimeList.Count} 条发放时间记录，开始批量插入数据库");

            // 批量插入数据库
            var affectedRows = await fSql.Insert(issueTimeList).ExecuteAffrowsAsync();
            logger.LogInformation($"成功插入 {affectedRows} 条发放时间记录");

            return issueTimeList[0];
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "创建发放时间数据时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 生成期号时间数据 - 当前日期前后3天的期号
    ///
    /// 功能：
    /// - 生成当前日期前3天到后3天的期号时间数据
    /// - 每天203期，从早上7点开始
    /// - 每期间隔5分钟，投注时长4分50秒
    /// - 自动处理跨年情况
    ///
    /// 数据范围：共7天的期号数据（约1421期）
    /// 期号格式：民国年份 + 3位序号
    /// </summary>
    /// <returns>期号时间数据列表</returns>
    private List<IssueTime> GenerateYearlyIssueTimeData()
    {
        // 临时变量，用于存放IssueTime对象
        var issueTimeList = new List<IssueTime>();

        // 确定起始时间
        var currentDate = DateTime.Now;
        var startDate = currentDate.Date.AddDays(-3);
        var endDate = currentDate.Date.AddDays(3);
        var startYear = startDate.Year;
        var endYear = endDate.Year;

        // 遍历年份
        for (int year = startYear; year <= endYear; year++)
        {
            var index = 0; // 全局序号计数器,按年循环
            var mingGuoYear = year - RepublicOfChinaFirstYear + 1; // 民国年份

            logger.LogDebug($"开始生成 {year} 年数据，民国年份: {mingGuoYear}");

            // 遍历月份
            for (int month = 1; month <= 12; month++)
            {
                var daysInMonth = DateTime.DaysInMonth(year, month);

                // 遍历日期
                for (int day = 1; day <= daysInMonth; day++)
                {
                    // 每天从开始时间前5分钟开始
                    var currentTime = new DateTime(year, month, day, StartHour, 0, 0).AddMinutes(-IntervalMinutes);

                    // 判断当前日期是否在有效期内
                    if (currentTime.Date < startDate.Date || currentTime.Date > endDate.Date)
                    {
                        index += DailyIssueCount;
                        continue;
                    }

                    // 遍历期数
                    for (int i = 0; i < DailyIssueCount; i++)
                    {
                        index++;

                        // 计算当前时间和序号
                        var openTime = currentTime.AddMinutes(IntervalMinutes);
                        var closeTime = openTime.AddMinutes(DurationMinutes).AddSeconds(-EarlyCloseSeconds).AddSeconds(-30);
                        var issueNumber = GenerateIssueNumber(mingGuoYear, index);

                        // 添加到列表
                        issueTimeList.Add(new IssueTime
                        {
                            Issue = issueNumber,
                            OpenTime = openTime,
                            CloseTime = closeTime
                        });

                        // 更新到下一个时间点
                        currentTime = openTime;
                    }
                }
            }

            // var daysInYear = DateTime.IsLeapYear(year) ? 366 : 365;
            // logger.LogDebug($"{year} 年共 {daysInYear} 天，生成 {index} 条发放记录");
        }

        return issueTimeList;
    }

    /// <summary>
    /// 生成期号编号 - 民国年份+序号格式
    ///
    /// 功能：
    /// - 按照民国年份+3位序号格式生成期号
    /// - 民国年份：公元年份-1911
    /// - 序号：3位数字，不足补0
    ///
    /// 示例：
    /// - 2024年第1期：113000001
    /// - 2024年第203期：113000203
    /// </summary>
    /// <param name="mingGuoYear">民国年份（公元年份-1911）</param>
    /// <param name="sequenceNumber">期号序号（1-203）</param>
    /// <returns>完整的期号编号</returns>
    private static string GenerateIssueNumber(int mingGuoYear, int sequenceNumber)
    {
        return $"{mingGuoYear}{sequenceNumber:D6}";
    }

    #endregion

    #region 数据维护方法

    /// <summary>
    /// 更新当前期号时间信息 - 实时监控循环
    ///
    /// 功能：
    /// - 持续监控当前期号状态
    /// - 实时更新开盘/封盘倒计时
    /// - 自动切换到下一期
    /// - 维护期号时间缓存
    ///
    /// 运行机制：
    /// - 每秒检查一次当前时间
    /// - 智能缓存策略减少数据库查询
    /// - 自动处理期号切换
    /// - 支持取消令牌优雅停止
    /// </summary>
    /// <param name="token">取消令牌</param>
    public async Task UpdateCurrentIssueTimeAsync(CancellationToken token)
    {
        logger.LogInformation(@"IssueTimeService更新循环已启动");

        try
        {
            while (!token.IsCancellationRequested)
            {
                try
                {
                    // 在循环开始时检查取消令牌
                    token.ThrowIfCancellationRequested();

                    await Task.Delay(1000, token);
                    DateTime now = DateTime.Now;

                    // 检查缓存是否有效（使用读写锁优化性能）
                    _cacheRwLock.EnterReadLock();
                    try
                    {
                        if (_cachedCurrentIssueTime != null && IsCacheValid(_cachedCurrentIssueTime, now))
                        {
                            logger.LogDebug($"使用缓存数据: {_cachedCurrentIssueTime.Issue}");
                            _openTimeSpan = (int)(_cachedCurrentIssueTime.OpenTime - now).TotalSeconds;
                            _closeTimeSpan = (int)(_cachedCurrentIssueTime.CloseTime - now).TotalSeconds;
                            Debug.WriteLine($"当前期号: {_cachedCurrentIssueTime.Issue} - 开奖倒计时: {_openTimeSpan} - 投注倒计时: {_closeTimeSpan}");
                            continue;
                        }
                    }
                    finally
                    {
                        _cacheRwLock.ExitReadLock();
                    }

                    // 再次检查取消令牌
                    token.ThrowIfCancellationRequested();

                    // 缓存无效，需要重新查询数据库
                    logger.LogDebug($"缓存过期，重新查询数据库 - 当前时间: {now:yyyy-MM-dd HH:mm:ss}");
                    IssueTime? resultIssueTime = null;

                    // 优化：使用单个查询同时查找当前和下一个时间段
                    var candidateIssues = await fSql.Select<IssueTime>()
                        .Where(x => x.CloseTime >= now || x.OpenTime > now) // 包含当前正在进行的和未来的时间段
                        .OrderBy(x => x.OpenTime)
                        .Take(2) // 最多取2条：当前进行中的和下一个
                        .ToListAsync(token);

                    if (candidateIssues.Count > 0)
                    {
                        // 优先选择当前正在进行的时间段
                        var currentIssue = candidateIssues.FirstOrDefault(x => x.OpenTime <= now && x.CloseTime >= now);
                        if (currentIssue != null)
                        {
                            resultIssueTime = currentIssue;
                            logger.LogInformation($"找到当前进行中的发放时间段: {currentIssue.Issue} ({currentIssue.OpenTime:HH:mm:ss} - {currentIssue.CloseTime:HH:mm:ss})");
                        }
                        else
                        {
                            // 没有当前进行的，选择最近的未来时间段
                            var nextIssue = candidateIssues.FirstOrDefault(x => x.OpenTime > now);
                            if (nextIssue != null)
                            {
                                resultIssueTime = nextIssue;
                                logger.LogInformation($"找到下一个发放时间段: {nextIssue.Issue} ({nextIssue.OpenTime:HH:mm:ss} - {nextIssue.CloseTime:HH:mm:ss})");
                            }
                        }
                    }

                    // 更新缓存
                    if (resultIssueTime != null)
                    {
                        UpdateCache(resultIssueTime);
                    }
                }
                catch (OperationCanceledException)
                {
                    logger.LogInformation(@"IssueTimeService更新循环被取消");
                    break;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"IssueTimeService更新循环发生异常");
                    // 异常时也要传递取消令牌
                    await Task.Delay(1000, token);
                }
            }
        }
        catch (OperationCanceledException)
        {
            logger.LogInformation(@"IssueTimeService更新循环已正常取消");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"IssueTimeService更新循环发生未处理异常");
        }
        finally
        {
            logger.LogInformation(@"IssueTimeService更新循环已退出");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新缓存（线程安全）
    /// 使用写锁确保更新操作的原子性和一致性
    /// 同时更新期号信息和状态，确保数据一致性
    /// </summary>
    /// <param name="issueTime">要缓存的发放时间对象</param>
    private void UpdateCache(IssueTime issueTime)
    {
        _cacheRwLock.EnterWriteLock();
        try
        {
            _cachedCurrentIssueTime = issueTime;
            logger.LogInformation($"缓存已更新: {issueTime.Issue} ({issueTime.OpenTime:HH:mm:ss} - {issueTime.CloseTime:HH:mm:ss})");
        }
        finally
        {
            _cacheRwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 清除缓存
    /// 强制下次查询时重新从数据库获取数据
    /// 使用写锁确保清除操作的原子性
    /// 同时重置状态为Unknown
    /// </summary>
    public void ClearCache()
    {
        _cacheRwLock.EnterWriteLock();
        try
        {
            var hadCache = _cachedCurrentIssueTime != null;

            _cachedCurrentIssueTime = null;

            if (hadCache)
            {
                logger.LogInformation("发放时间缓存已清除");
            }
        }
        finally
        {
            _cacheRwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 获取上期期号信息
    /// </summary>
    /// <param name="currentIssue">当前期号</param>
    /// <param name="token">取消令牌</param>
    /// <returns>上期期号信息，如果不存在则返回null</returns>
    public async Task<IssueTime?> GetPreviousIssueAsync(string currentIssue, CancellationToken token)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(currentIssue))
            {
                logger.LogWarning(@"获取上期期号失败，当前期号为空");
                return null;
            }

            // 获取当前期号对应的IssueTime记录
            var currentIssueTime = await fSql.Select<IssueTime>()
                .Where(x => x.Issue == currentIssue)
                .FirstOrDefaultAsync(token);

            if (currentIssueTime == null)
            {
                logger.LogWarning(@"未找到当前期号的时间信息: {CurrentIssue}", currentIssue);
                return null;
            }

            // 基于时间查询上期：查询开奖时间小于当前期开奖时间的最近一期
            var previousIssue = await fSql.Select<IssueTime>()
                .Where(x => x.OpenTime < currentIssueTime.OpenTime)
                .OrderByDescending(x => x.OpenTime)
                .FirstOrDefaultAsync(token);

            if (previousIssue != null)
            {
                logger.LogDebug(@"找到上期期号: {PreviousIssue} (开奖时间: {PreviousOpenTime}), 当前期号: {CurrentIssue} (开奖时间: {CurrentOpenTime})",
                    previousIssue.Issue, previousIssue.OpenTime, currentIssue, currentIssueTime.OpenTime);
            }
            else
            {
                logger.LogDebug(@"未找到上期期号, 当前期号: {CurrentIssue} (开奖时间: {CurrentOpenTime})",
                    currentIssue, currentIssueTime.OpenTime);
            }

            return previousIssue;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取上期期号失败，当前期号: {CurrentIssue}", currentIssue);
            return null;
        }
    }

    /// <summary>
    /// 检查缓存是否仍然有效
    /// 优化后的缓存策略：最大化缓存利用率，最小化数据库查询
    /// </summary>
    /// <param name="cachedIssueTime">缓存的发放时间对象</param>
    /// <param name="currentTime">当前时间</param>
    /// <returns>如果缓存有效返回true，否则返回false</returns>
    private bool IsCacheValid(IssueTime cachedIssueTime, DateTime currentTime)
    {
        // 情况1：当前时间在发放时间段内 - 缓存绝对有效
        if (currentTime <= cachedIssueTime.CloseTime)
        {
            logger.LogTrace($"缓存有效：当前时间在发放时间段内 ({cachedIssueTime.OpenTime:HH:mm:ss} - {cachedIssueTime.CloseTime:HH:mm:ss})");
            return true;
        }

        return false;
    }

    #endregion

    #region 资源管理和释放

    /// <summary>
    /// 释放服务占用的系统资源
    ///
    /// 功能：确保服务关闭时正确释放所有占用的系统资源
    ///
    /// 释放资源：
    /// - ReaderWriterLockSlim：读写锁对象
    /// - 清理缓存引用：避免内存泄漏
    /// - 重置状态：确保服务状态清洁
    ///
    /// 调用时机：
    /// - 应用程序关闭时
    /// - 服务容器释放时
    /// - 手动释放资源时
    ///
    /// 最佳实践：
    /// - 实现IDisposable接口
    /// - 支持多次调用Dispose
    /// - 释放后标记对象状态
    ///
    /// 线程安全：
    /// - Dispose方法本身是线程安全的
    /// - 释放过程中会阻塞其他操作
    /// - 释放后的对象不应再被使用
    ///
    /// 注意事项：
    /// - 释放后不应再调用任何业务方法
    /// - 建议在应用程序关闭时自动调用
    /// - 可以通过依赖注入容器自动管理
    /// </summary>
    public void Dispose()
    {
        try
        {
            // 释放读写锁资源
            _cacheRwLock.Dispose();

            // 清理缓存引用
            _cachedCurrentIssueTime = null;

            logger.LogInformation("期号时间服务资源已释放");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "释放期号时间服务资源时发生异常");
        }
    }

    #endregion

    /// <summary>
    /// 清空期号时间信息 - 危险操作
    ///
    /// 功能：
    /// - 删除IssueTime表中的所有期号时间记录
    /// - 清空期号生成和时间管理的基础数据
    /// - 支持异步操作和取消令牌
    ///
    /// ⚠️ 警告：
    /// - 这是一个危险操作，会删除所有期号时间数据
    /// - 执行后需要重新生成期号时间数据
    /// - 会影响整个期号系统的正常运行
    ///
    /// 使用场景：
    /// - 系统初始化或重置
    /// - 数据清理和重建
    /// - 测试环境数据清理
    ///
    /// 注意：此方法清空的是期号时间信息，不是开奖结果信息
    /// </summary>
    public Task ClearIssueTimeAsync(CancellationToken token)
    {
        logger.LogInformation("清空IssueTime数据");
        return fSql.Delete<IssueTime>().Where("1=1").ExecuteAffrowsAsync(token);
    }
}